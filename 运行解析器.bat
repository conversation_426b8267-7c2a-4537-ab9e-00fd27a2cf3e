@echo off
chcp 65001 >nul
echo ========================================
echo    防火墙会话日志解析器
echo ========================================
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 使用完整Python路径...
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe" session_parser.py
) else (
    echo 使用系统Python...
    python session_parser.py
)

echo.
echo ========================================
echo 按任意键退出...
pause >nul
