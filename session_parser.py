#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
防火墙会话日志解析器
解析华为防火墙的IPv4和IPv6会话信息，生成Excel报告
"""

import re
import pandas as pd
from datetime import datetime
import os
import sys

class FirewallSessionParser:
    def __init__(self):
        self.ipv4_sessions = []
        self.ipv6_sessions = []
        
    def parse_txt_file(self, file_path):
        """解析txt文件中的会话信息"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
        except UnicodeDecodeError:
            # 如果UTF-8解码失败，尝试GBK编码
            with open(file_path, 'r', encoding='gbk') as file:
                content = file.read()
        
        # 分割IPv4和IPv6会话部分
        ipv4_section = self._extract_ipv4_section(content)
        ipv6_section = self._extract_ipv6_section(content)
        
        # 解析IPv4会话
        if ipv4_section:
            self.ipv4_sessions = self._parse_sessions(ipv4_section, 'IPv4')
            
        # 解析IPv6会话
        if ipv6_section:
            self.ipv6_sessions = self._parse_sessions(ipv6_section, 'IPv6')
            
        print(f"解析完成: IPv4会话 {len(self.ipv4_sessions)} 条, IPv6会话 {len(self.ipv6_sessions)} 条")
        
    def _extract_ipv4_section(self, content):
        """提取IPv4会话部分"""
        # 查找IPv4会话开始和结束位置
        ipv4_start = content.find('dis aft session ipv4')
        ipv6_start = content.find('dis aft session ipv6')
        
        if ipv4_start == -1:
            return None
            
        if ipv6_start == -1:
            return content[ipv4_start:]
        else:
            return content[ipv4_start:ipv6_start]
    
    def _extract_ipv6_section(self, content):
        """提取IPv6会话部分"""
        ipv6_start = content.find('dis aft session ipv6')
        if ipv6_start == -1:
            return None
        return content[ipv6_start:]
    
    def _parse_sessions(self, section, ip_version):
        """解析会话信息"""
        sessions = []
        
        # 使用正则表达式匹配会话块
        session_pattern = r'Initiator:\s*\n(.*?)(?=\nInitiator:|\nTotal sessions found:|\nCPU|\Z)'
        session_blocks = re.findall(session_pattern, section, re.DOTALL)
        
        for block in session_blocks:
            session_info = self._parse_session_block(block, ip_version)
            if session_info:
                sessions.append(session_info)
                
        return sessions
    
    def _parse_session_block(self, block, ip_version):
        """解析单个会话块"""
        session_info = {'IP版本': ip_version}
        
        # 解析源IP和端口
        source_match = re.search(r'Source\s+IP/port:\s*([^/\s]+)/(\d+)', block)
        if source_match:
            session_info['源IP'] = source_match.group(1)
            session_info['源端口'] = int(source_match.group(2))
        
        # 解析目标IP和端口
        dest_match = re.search(r'Destination\s+IP/port:\s*([^/\s]+)/(\d+)', block)
        if dest_match:
            session_info['目标IP'] = dest_match.group(1)
            session_info['目标端口'] = int(dest_match.group(2))
        
        # 解析协议
        protocol_match = re.search(r'Protocol:\s*([^(\s]+)', block)
        if protocol_match:
            session_info['协议'] = protocol_match.group(1)
        
        # 解析入接口
        interface_match = re.search(r'Inbound interface:\s*([^\n\r]+)', block)
        if interface_match:
            session_info['入接口'] = interface_match.group(1).strip()
        
        # 解析安全域
        zone_match = re.search(r'Source security zone:\s*([^\n\r]+)', block)
        if zone_match:
            session_info['源安全域'] = zone_match.group(1).strip()
        
        # 解析VPN实例信息
        vpn_match = re.search(r'VPN instance/VLAN ID/Inline ID:\s*([^\n\r]+)', block)
        if vpn_match:
            session_info['VPN实例/VLAN/Inline'] = vpn_match.group(1).strip()
        
        # 解析DS-Lite隧道信息
        tunnel_match = re.search(r'DS-Lite tunnel peer:\s*([^\n\r]+)', block)
        if tunnel_match:
            session_info['DS-Lite隧道'] = tunnel_match.group(1).strip()
        
        return session_info if '源IP' in session_info and '目标IP' in session_info else None
    
    def generate_excel_report(self, output_file='防火墙会话报告.xlsx'):
        """生成Excel报告"""
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 创建汇总表
            self._create_summary_sheet(writer)
            
            # 创建IPv4会话表
            if self.ipv4_sessions:
                ipv4_df = pd.DataFrame(self.ipv4_sessions)
                ipv4_df.to_excel(writer, sheet_name='IPv4会话', index=False)
                self._format_worksheet(writer.sheets['IPv4会话'])
            
            # 创建IPv6会话表
            if self.ipv6_sessions:
                ipv6_df = pd.DataFrame(self.ipv6_sessions)
                ipv6_df.to_excel(writer, sheet_name='IPv6会话', index=False)
                self._format_worksheet(writer.sheets['IPv6会话'])
            
            # 创建统计分析表
            self._create_analysis_sheet(writer)
        
        print(f"Excel报告已生成: {output_file}")
    
    def _create_summary_sheet(self, writer):
        """创建汇总表"""
        summary_data = {
            '项目': ['IPv4会话总数', 'IPv6会话总数', '总会话数', '生成时间'],
            '数值': [
                len(self.ipv4_sessions),
                len(self.ipv6_sessions), 
                len(self.ipv4_sessions) + len(self.ipv6_sessions),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ]
        }
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='汇总', index=False)
        self._format_worksheet(writer.sheets['汇总'])
    
    def _create_analysis_sheet(self, writer):
        """创建统计分析表"""
        analysis_data = []
        
        # 合并所有会话数据进行分析
        all_sessions = self.ipv4_sessions + self.ipv6_sessions
        
        if all_sessions:
            # 按协议统计
            protocol_stats = {}
            for session in all_sessions:
                protocol = session.get('协议', '未知')
                protocol_stats[protocol] = protocol_stats.get(protocol, 0) + 1
            
            # 按端口统计（目标端口）
            port_stats = {}
            for session in all_sessions:
                port = session.get('目标端口', '未知')
                port_stats[port] = port_stats.get(port, 0) + 1
            
            # 按安全域统计
            zone_stats = {}
            for session in all_sessions:
                zone = session.get('源安全域', '未知')
                zone_stats[zone] = zone_stats.get(zone, 0) + 1
            
            # 生成分析数据
            analysis_data.extend([
                {'分析类型': '协议统计', '项目': k, '数量': v} 
                for k, v in sorted(protocol_stats.items(), key=lambda x: x[1], reverse=True)
            ])
            
            analysis_data.append({'分析类型': '', '项目': '', '数量': ''})  # 空行分隔
            
            analysis_data.extend([
                {'分析类型': '目标端口统计', '项目': k, '数量': v} 
                for k, v in sorted(port_stats.items(), key=lambda x: x[1], reverse=True)[:10]  # 只显示前10个
            ])
            
            analysis_data.append({'分析类型': '', '项目': '', '数量': ''})  # 空行分隔
            
            analysis_data.extend([
                {'分析类型': '安全域统计', '项目': k, '数量': v} 
                for k, v in sorted(zone_stats.items(), key=lambda x: x[1], reverse=True)
            ])
        
        if analysis_data:
            analysis_df = pd.DataFrame(analysis_data)
            analysis_df.to_excel(writer, sheet_name='统计分析', index=False)
            self._format_worksheet(writer.sheets['统计分析'])
    
    def _format_worksheet(self, worksheet):
        """格式化工作表"""
        # 自动调整列宽
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)  # 最大宽度限制为50
            worksheet.column_dimensions[column_letter].width = adjusted_width

def main():
    """主函数"""
    print("=== 防火墙会话日志解析器 ===")

    # 检查命令行参数
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        # 自动查找txt文件
        txt_files = [f for f in os.listdir('.') if f.endswith('.txt')]
        if not txt_files:
            print("错误: 当前目录中没有找到txt文件")
            print("请将防火墙会话日志文件放在当前目录中")
            return
        elif len(txt_files) == 1:
            input_file = txt_files[0]
            print(f"自动选择文件: {input_file}")
        else:
            print("发现多个txt文件，请选择:")
            for i, file in enumerate(txt_files, 1):
                print(f"{i}. {file}")
            try:
                choice = int(input("请输入文件编号: ")) - 1
                if 0 <= choice < len(txt_files):
                    input_file = txt_files[choice]
                else:
                    print("无效的选择")
                    return
            except ValueError:
                print("无效的输入")
                return

    if not os.path.exists(input_file):
        print(f"错误: 找不到输入文件 '{input_file}'")
        print("请确保文件存在于当前目录中")
        return
    
    # 创建解析器实例
    parser = FirewallSessionParser()
    
    try:
        # 解析文件
        print(f"正在解析文件: {input_file}")
        parser.parse_txt_file(input_file)
        
        # 生成Excel报告
        output_file = f"防火墙会话报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        print(f"正在生成Excel报告: {output_file}")
        parser.generate_excel_report(output_file)
        
        print("\n=== 解析完成 ===")
        print(f"IPv4会话: {len(parser.ipv4_sessions)} 条")
        print(f"IPv6会话: {len(parser.ipv6_sessions)} 条")
        print(f"总计: {len(parser.ipv4_sessions) + len(parser.ipv6_sessions)} 条")
        print(f"报告文件: {output_file}")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
