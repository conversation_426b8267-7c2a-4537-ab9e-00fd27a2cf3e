# Python环境问题说明

## 问题原因

你的系统中存在**Windows应用商店Python重定向器**的问题，这是Windows 10/11的一个常见问题。

### 具体情况：
1. 你已经正确安装了Python 3.12在：`C:\Users\<USER>\AppData\Local\Programs\Python\Python312\`
2. PATH环境变量也正确包含了Python路径
3. 但是Windows应用商店在 `C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\` 中放置了0字节的`python.exe`重定向器
4. 这个重定向器在PATH中的优先级更高，导致直接运行`python`命令时失败

## 解决方案

### 方案1：使用批处理文件（推荐）
直接双击 `运行解析器.bat` 文件，它会使用完整路径调用Python

### 方案2：禁用Windows应用商店Python重定向器
1. 打开Windows设置
2. 搜索"应用执行别名"或"App execution aliases"
3. 找到"Python"相关的开关
4. 关闭"应用安装程序 python.exe"和"应用安装程序 python3.exe"

### 方案3：使用完整路径
在命令行中使用：
```cmd
"C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe" session_parser.py
```

### 方案4：使用py启动器
Windows Python安装通常包含py启动器：
```cmd
py session_parser.py
```

## 验证Python安装

运行以下命令验证Python是否正常工作：
```cmd
"C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe" --version
```

应该显示：`Python 3.12.x`

## 为什么会有这个问题？

Windows 10/11 默认在Microsoft Store中提供Python，为了引导用户安装，系统会在WindowsApps目录中放置重定向器。当用户直接输入`python`时，如果没有安装Store版本的Python，这些重定向器就会失败。

## 推荐做法

1. **使用批处理文件**：最简单，无需修改系统设置
2. **关闭应用执行别名**：一劳永逸，但需要修改系统设置
3. **使用py启动器**：Python官方推荐的方式

你的Python安装是完全正常的，只是Windows的重定向机制导致了这个问题。
