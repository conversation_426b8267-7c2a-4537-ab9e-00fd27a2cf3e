# 防火墙会话日志解析器使用说明

## 功能介绍
这个Python脚本可以解析华为防火墙的会话详情日志文件，提取IPv4和IPv6会话信息，并生成详细的Excel报告。

## 功能特点
- 自动识别和分离IPv4、IPv6会话
- 提取详细的会话信息（源IP、目标IP、端口、协议、接口、安全域等）
- 生成多工作表Excel报告
- 包含统计分析功能
- 支持中文显示

## 安装依赖
在运行脚本前，需要安装以下Python包：

```bash
pip install pandas openpyxl
```

## 使用方法

### 方法1: 直接运行
确保你的txt文件名为 `FW07 会话详情.txt` 并放在脚本同一目录下，然后运行：

```bash
python session_parser.py
```

### 方法2: 修改文件名
如果你的txt文件名不同，可以修改脚本中的 `input_file` 变量，或者重命名你的文件。

## 输出文件
脚本会生成一个Excel文件，文件名格式为：`防火墙会话报告_YYYYMMDD_HHMMSS.xlsx`

## Excel报告内容

### 1. 汇总表
- IPv4会话总数
- IPv6会话总数  
- 总会话数
- 生成时间

### 2. IPv4会话表
包含所有IPv4会话的详细信息：
- IP版本
- 源IP
- 源端口
- 目标IP
- 目标端口
- 协议
- 入接口
- 源安全域
- VPN实例/VLAN/Inline
- DS-Lite隧道

### 3. IPv6会话表
包含所有IPv6会话的详细信息（字段同IPv4）

### 4. 统计分析表
- 按协议统计
- 按目标端口统计（显示前10个）
- 按安全域统计

## 支持的数据格式
脚本支持解析华为防火墙以下命令的输出：
- `dis nat session`
- `dis aft session ipv4`
- `dis aft session ipv6`

## 注意事项
1. 确保txt文件编码为UTF-8或GBK
2. 脚本会自动处理编码问题
3. 如果解析失败，请检查文件格式是否正确
4. 生成的Excel文件会自动调整列宽以便查看

## 错误处理
如果遇到问题，脚本会显示详细的错误信息。常见问题：
- 文件不存在：检查文件名和路径
- 编码错误：脚本会自动尝试不同编码
- 解析错误：检查文件格式是否为华为防火墙输出

## 示例输出
```
=== 防火墙会话日志解析器 ===
正在解析文件: FW07 会话详情.txt
解析完成: IPv4会话 33 条, IPv6会话 35 条
正在生成Excel报告: 防火墙会话报告_20250801_143022.xlsx
Excel报告已生成: 防火墙会话报告_20250801_143022.xlsx

=== 解析完成 ===
IPv4会话: 33 条
IPv6会话: 35 条
总计: 68 条
报告文件: 防火墙会话报告_20250801_143022.xlsx
```
